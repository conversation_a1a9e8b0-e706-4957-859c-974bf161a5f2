import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';


const routes: Routes = [
  { path: '', loadChildren: () => import('./components/equipment/equipment-module/equipment-module-module').then(m => m.EquipmentModuleModule) },
  // { path: 'equipment', component: EquipmentList,},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
