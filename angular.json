{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"oneDC": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./src/styles.scss", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "./src/assets/css/custom-style.css", "./src/assets/styles/style-common.css"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "develop": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.develop.ts"}], "outputHashing": "all"}, "uat": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "outputHashing": "all"}, "prod": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "oneDC:build:production"}, "development": {"buildTarget": "oneDC:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"]}}}}}, "cli": {"analytics": "98722aa2-929d-448f-9612-ef21f33f9030"}}