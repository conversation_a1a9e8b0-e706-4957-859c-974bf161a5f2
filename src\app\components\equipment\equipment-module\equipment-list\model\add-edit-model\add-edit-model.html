<div class="row">
    <form [formGroup]="equipmentForm">
        <div class="row mb-2">
            <div class="col-lg-4 col-md-4 section-item">
            <label class="ml-4">Equipment Type <span class="text-danger">*</span></label>
                <select class="form-select form-control custom-dropdown" formControlName="equipmentType">
                    <option value='Modules'>Module</option>
                    <option value='Inverters'>Invertors</option>
                    <option value='Optimizers'>Optimizers</option>
                    <option value='Rails'>Rails</option>
                    <option value='Racking/Fastener'>Racking/Fastener</option>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-4 section-item">
                <label class="ml-4">Model Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="modelName" placeholder="Enter Model Name" autocomplete="off" formControlName="modelName" autocomplete="off"/>
            </div>
            <div class="col-lg-4 col-md-4 section-item">
                <label class="ml-4">Manufacturer<span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="manufacturer" placeholder="Enter Manufacturer" autocomplete="off" formControlName="manufacturer" autocomplete="off"/>
            </div>
            <div class="col-lg-4 col-md-4 section-item">
                <label class="ml-4">Model ID<span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="modelId" placeholder="Enter Model ID" autocomplete="off" formControlName="modelId" autocomplete="off"/>
            </div>
        </div>
    </form>
</div>
<ng-template #modalFooterTemplate let-modal>
    <button type="button" nz-button nzType="default" class="me-1" (click)="resetForm()"><span><i class="fa fa-refresh fa-solid me-2"></i></span>Reset</button>
    <button type="submit" nz-button nzType="primary" class="custom-pad-btn" [disabled]="equipmentForm.invalid" (click)="onSubmit()">
        <span><i class="fa fa-floppy-o fa-solid me-2"></i></span>Save
    </button>
</ng-template>
