import { Component, signal, ViewChild } from '@angular/core';
import { MsalService } from '@azure/msal-angular';
import { EventType } from '@azure/msal-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { HeaderComponent } from './components/shared-components/header/header.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.html',
  standalone: false,
  styleUrl: './app.scss'
})
export class App {
  protected readonly title = signal('oneDC');
  constructor(
    private msalService: MsalService,
    private router: Router, private route: ActivatedRoute,    
    ) { }

  userName: string | null = '';
  @ViewChild(HeaderComponent) headerComponent!: HeaderComponent;

  // async ngOnInit(): Promise<void> {
  //   // this.loaderService.invokeLoaderComponent(true);

  //   // Initialize MSAL instance before using it
  //   try {
  //     await this.msalService.instance.initialize();
  //     this.checkAccountStatus();
  //   } catch (error) {
  //     console.error('MSAL initialization failed:', error);
  //     // this.loaderService.invokeLoaderComponent(false);
  //   }
  // }

  checkAccountStatus() {
    const accounts = this.msalService.instance.getAllAccounts();
    if (accounts.length > 0) {
      localStorage.setItem('userName', String(this.msalService.instance.getActiveAccount()?.name));
      localStorage.setItem('userEmail', String(this.msalService.instance.getActiveAccount()?.username));      
      this.msalService.instance.setActiveAccount(accounts[0]);
      this.userName = localStorage.getItem('userName');
      // this.authenticate();

    } else {
      this.msalService.instance.addEventCallback((event: any) => {
        if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
          const account = event.payload.account;
          this.msalService.instance.setActiveAccount(account);
        }
      });

      this.msalService.instance.handleRedirectPromise().then((res => {
        const account = this.msalService.instance.getActiveAccount();
        if (!account) {
          this.msalService.loginRedirect();
        }
        else {
          if (res != null && res.account != null) {
            this.msalService.instance.setActiveAccount(res.account);
            localStorage.setItem('userName', String(this.msalService.instance.getActiveAccount()?.name));
            localStorage.setItem('userEmail', String(this.msalService.instance.getActiveAccount()?.username)); 
            this.userName = localStorage.getItem('userName')
            // this.authenticate();
          } else {
            this.msalService.loginRedirect();
          }
        }
      })).catch(err => {
        console.error(err);
      });
    }
  }

  /**
   * Logout
   * @param $event
   */
  logout(): void {
    const activeAccount = this.msalService.instance.getActiveAccount();
    if (activeAccount) {
      localStorage.clear();
      this.msalService.logout();
    } else {
      console.warn("No active account to logout.");
    }
  }
}
