import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { EquipmentModuleRoutingModule } from './equipment-module-routing-module';
import { Equipment } from './equipment/equipment';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { EquipmentList } from './equipment-list/equipment-list';
import { AddEditModel } from './equipment-list/model/add-edit-model/add-edit-model';


@NgModule({
  declarations: [
    Equipment,
    EquipmentList,
    AddEditModel
  ],
  imports: [
    CommonModule,
    EquipmentModuleRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NzModalModule,
    NzButtonModule,
    NzCardModule,
    BrowserAnimationsModule
  ]
})
export class EquipmentModuleModule { }
