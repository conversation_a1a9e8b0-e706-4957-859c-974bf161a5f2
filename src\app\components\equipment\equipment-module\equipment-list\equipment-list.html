<div id="oneDC-form">
    <div class="row">
        <div class="col-lg-2 col-md-3 section-item mt-1">
            <label class="ml-4">Finance Partner</label>
            <select class="form-select form-control custom-dropdown">
                <option value=null disabled>Select</option>
                <option value='igs'>IGS</option>
                <option value='goodleap'>Goodleap</option>
                <option value='sunlight'>Sunlight</option>
                <option value='lightreach'>Lightreach</option>

            </select>
        </div>
        <div class="col-lg-3 col-md-2 section-item">
            <div class="search-btn">
                <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search">
                    <i aria-hidden="true" class="fa fa-search"></i></button>
                <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
                    title="Clear">
                    <img [src]="'./assets/images/clear-search-icon.svg'" height="15px">
                </button>
                <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Add"
                    (click)="addEquipment()">
                    <i aria-hidden="true" class="fa fa-plus"></i></button>
            </div>
        </div>
    </div>
    <div class="row mt-2">
        <div class="col section-item" style="padding-right: -10px !important;"
            *ngFor="let category of categories; let ci = index">
            <nz-card class="mb-3" [nzTitle]="category.title" nzBordered="true" nzSize="default">
                <div *ngFor="let item of category.items; let ii = index"
                    class="border rounded p-2 mb-2 d-flex flex-column">
                    <div class="fw-bold">{{ item.modelName }}</div>
                    <small class="text-muted">{{ item.manufacturer }}</small>
                    <div class="d-flex justify-content-between align-items-center">
                        <small>{{ item.modelId }}</small>
                        <div>
                            <i class="fa fa-pencil-square mx-1 text-primary fs-6" 
                                (click)="editItem(ci, ii)"></i>
                            <i class="fa fa-trash text-danger fs-6" 
                                (click)="deleteItem(ci, ii)"></i>
                        </div>
                    </div>

                </div>

            </nz-card>

        </div>

    </div>

</div>