// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  authUrl: 'https://devopsiis.trinity-solar.com:5020/UAMAPI/api/',
  appBaseURL: 'http://localhost:4200',
  oneDCUrl: 'https://devopsiis.trinity-solar.com:5020/onePM-API/',  
  authScope: 'api://8e9e1239-7c87-4036-bd01-780af49d160c/onePM-dev',
  authClientId: '8e9e1239-7c87-4036-bd01-780af49d160c',
  authority: 'https://login.microsoftonline.com/trinity-solar.com',
  authRedirectURL: 'http://localhost:4200',
  appDetails: {
    appId: 98,
  },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
