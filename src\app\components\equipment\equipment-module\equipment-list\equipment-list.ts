import { Component, OnInit } from '@angular/core';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { AddEditModel } from './model/add-edit-model/add-edit-model';
interface Item {
  modelName: string;
  manufacturer: string;
  modelId: string;
}

interface Category {
  title: string;
  items: Item[];
}

@Component({
  selector: 'app-equipment-list',
  standalone: false,
  templateUrl: './equipment-list.html',
  styleUrl: './equipment-list.scss'
})
export class EquipmentList implements OnInit{
  categories: Category[] = [
    {
      title: 'Modules',
      items: [
        { modelName: 'Q.Peak Duo ML-G10+', manufacturer: 'QCell', modelId: 'MD06G100A-017' },
        { modelName: 'Q.Peak Duo ML-G10.C+', manufacturer: 'QCell', modelId: 'MD06G100A-999' }
      ]
    },
    {
      title: 'Inverters',
      items: [
        { modelName: 'Home Hub - BL15', manufacturer: 'SolarEdge', modelId: 'SExxxxH-USMNUBL15' },
        { modelName: 'IQ8HC Microinverter', manufacturer: 'Enphase', modelId: 'IQ8HC-72-M-DOM-US' },
        { modelName: 'Home Hub - BE78', manufacturer: 'SolarEdge', modelId: 'USExxxxH-USMNBE78' },
        { modelName: 'Home Hub - BE78', manufacturer: 'SolarEdge', modelId: 'USExxxxH-USMNBE78' },
        { modelName: 'Home Hub - BE78', manufacturer: 'SolarEdge', modelId: 'USExxxxH-USMNBE78' },
        { modelName: 'Home Hub - BE78', manufacturer: 'SolarEdge', modelId: 'USExxxxH-USMNBE78' },
      ]
    },
    {
      title: 'Optimizers',
      items: [
        { modelName: '650W Optimizer', manufacturer: 'SolarEdge', modelId: 'U650/U650B' }
      ]
    },
    {
      title: 'Rails',
      items: [
        { modelName: 'NXT UMOUNT RAIL - 171" MILL (US)', manufacturer: 'Unirac', modelId: '171RLM1-US' },
        { modelName: 'CF STD US RAIL AL MLL 171.5"', manufacturer: 'Ecofasten', modelId: '2012035' },
        { modelName: 'SKIRT US AL BLK 82"', manufacturer: 'Ecofasten', modelId: '2099044' }
      ]
    },
    {
      title: 'Racking/Fastener',
      items: [
        { modelName: 'SM BND SPLICE BAR PRO SERIES MILL', manufacturer: 'Unirac', modelId: '303019M-US' },
        { modelName: 'SM BND MIDCLAMP BC DRK SS (US)', manufacturer: 'Unirac', modelId: '302027D-US' },
        { modelName: 'SM ENDCLAMP B DRK AL (US)', manufacturer: 'Unirac', modelId: '302021D-US' }
      ]
    },
    
  ];
  constructor(private modalService: NzModalService) {}
  ngOnInit(): void {
  }
  addEquipment(){
    this.showEquipmentModel('Add Equipment');
  }
  showEquipmentModel(title:string,modelData?:any){
    const modal: NzModalRef = this.modalService.create({
      nzTitle: title,
      nzContent: AddEditModel,
      nzFooter: null,
      nzMaskClosable: false,
      nzData: {modelData},
      nzWidth:"700px"
    });
    setTimeout(() => {
      const componentInstance = modal.getContentComponent() as AddEditModel;
      modal.updateConfig({
        nzFooter: componentInstance.modalFooterTemplate,
      });
    });
    modal.afterClose.subscribe((result: any) => {
      if(result){
      }
    });
  }
  editItem(categoryIndex: number, itemIndex: number) {
    let passData ={
      title:this.categories[categoryIndex].title,
      items:this.categories[categoryIndex].items[itemIndex]
    }
    this.showEquipmentModel('Edit Equipment',passData);
  }
  deleteItem(categoryIndex: number, itemIndex: number) {
     this.modalService.confirm({
      nzTitle: '',
      nzContent: 'Are you sure you want to delete equipment?',
      nzOkText: 'OK',
      nzCancelText: 'Cancel',
      nzClosable: false,
      nzOnOk: () => {
        this.categories[categoryIndex].items.splice(itemIndex, 1);
      },
      nzOnCancel: () => {
        this.modalService.closeAll();
      },
    });
  }
}
