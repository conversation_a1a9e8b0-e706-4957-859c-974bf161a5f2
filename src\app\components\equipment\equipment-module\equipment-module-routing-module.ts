import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Equipment } from './equipment/equipment';
import { EquipmentList } from './equipment-list/equipment-list';

const routes: Routes = [
   {
    path: '',
    component: Equipment,
    children:[
      {path:'equipment',component:EquipmentList}
    ]
   }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EquipmentModuleRoutingModule { }
