import { NgModule, provideBrowserGlobalErrorListeners } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing-module';
import { App } from './app';
import { Header<PERSON>omponent } from './components/shared-components/header/header.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { MsalInterceptor, MsalService, MsalBroadcastService, MsalGuard, MSAL_INSTANCE, MSAL_INTERCEPTOR_CONFIG, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { MSALInstanceFactory, MSALInterceptorConfigFactory, MSALGuardConfigFactory } from './interceptor/msal.interceptor';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';

@NgModule({
  declarations: [
    App,
    HeaderComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    NzBreadCrumbModule
  ],
  providers: [
    provideBrowserGlobalErrorListeners(),
    {
            provide: HTTP_INTERCEPTORS,
            useClass: MsalInterceptor,
            multi: true
        },
        { provide: MSAL_INTERCEPTOR_CONFIG, useFactory: MSALInterceptorConfigFactory },
        { provide: MSAL_INSTANCE, useFactory: MSALInstanceFactory },
        { provide: MSAL_GUARD_CONFIG, useFactory: MSALGuardConfigFactory },
        MsalService,
        MsalBroadcastService,
        MsalGuard,
        provideHttpClient(withInterceptorsFromDi())
  ],
  bootstrap: [App]
})
export class AppModule { }
