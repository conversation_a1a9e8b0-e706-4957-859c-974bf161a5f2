import { Component, inject, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-add-edit-model',
  standalone: false,
  templateUrl: './add-edit-model.html',
  styleUrl: './add-edit-model.scss'
})
export class AddEditModel implements OnInit {
  @ViewChild('modalFooterTemplate', { static: true }) modalFooterTemplate!: TemplateRef<any>;
  equipmentForm!: FormGroup;
  data = inject(NZ_MODAL_DATA); 
   constructor(
    private formBuilder: FormBuilder,
    private modalRef: NzModalRef<AddEditModel>,
  ) { }
  ngOnInit(): void {
    this.initialEquipmentForm();
    this.setEquipmentData();
  }
  /**
   * Function is used to initialize team form 
   */
  initialEquipmentForm(): FormGroup {
    return this.equipmentForm = this.formBuilder.group({
      equipmentType: ['',[Validators.required]],
      modelName: ['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
      manufacturer: ['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
      modelId: ['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
    });
  }
  setEquipmentData(){
    if (this.data) {
      this.equipmentForm.setValue({
        equipmentType:this.data.modelData.title,
        modelName: this.data.modelData.items.modelName,
        manufacturer: this.data.modelData.items.manufacturer,
        modelId: this.data.modelData.items.modelId
      });
    }
  }
  resetForm(){
    this.equipmentForm.reset();
  }
  onSubmit(){
    console.log(this.equipmentForm.value)
  }
}
